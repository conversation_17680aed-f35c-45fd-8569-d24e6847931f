#!/usr/bin/env node

const NeuralCoordinator = require('../src/neural-coordinator');
const fs = require('fs');
const path = require('path');

async function runDemo() {
  console.log('🚀 Starting Mister Smith DAA Neural Network Demo...\n');
  
  // Load configuration
  const configPath = path.join(__dirname, '../config/ms-framework-config.json');
  const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
  
  // Create neural coordinator
  const coordinator = new NeuralCoordinator({
    id: 'ms-neural-coordinator',
    swarmId: 'ms-swarm-001',
    neuralConfig: config.neuralConfig
  });
  
  // Initialize
  await coordinator.initialize();
  
  // Create agents based on configuration
  console.log('\n📊 Creating agent teams...');
  
  for (const [teamName, teamConfig] of Object.entries(config.teams)) {
    console.log(`\n🏢 Creating ${teamName} team:`);
    
    // Create team lead
    const lead = await coordinator.createAgent(
      `${teamName}-lead`,
      teamConfig.lead.type,
      {
        cognitivePattern: teamConfig.lead.cognitivePattern,
        capabilities: [`${teamName}-expertise`]
      }
    );
    console.log(`  ✅ ${teamConfig.lead.name} (${teamConfig.lead.cognitivePattern})`);
    
    // Create team members
    for (const member of teamConfig.members) {
      const agent = await coordinator.createAgent(
        `${teamName}-${member.name.toLowerCase().replace(/\s+/g, '-')}`,
        member.type,
        {
          cognitivePattern: member.cognitivePattern,
          capabilities: [`${teamName}-expertise`]
        }
      );
      console.log(`  ✅ ${member.name} (${member.cognitivePattern})`);
    }
  }
  
  console.log(`\n✅ Created ${coordinator.agents.size} agents total`);
  
  // Create workflows
  console.log('\n🔄 Setting up workflows...');
  
  for (const [workflowId, workflowConfig] of Object.entries(config.workflows)) {
    await coordinator.createWorkflow(workflowId, workflowConfig);
    console.log(`  ✅ ${workflowConfig.name}`);
  }
  
  // Demo task orchestration
  console.log('\n🎯 Demonstrating task orchestration...');
  
  const demoTasks = [
    {
      id: 'task-001',
      description: 'Implement async message handling with supervision trees',
      priority: 'high',
      complexity: 8
    },
    {
      id: 'task-002',
      description: 'Optimize database query performance for JetStream KV',
      priority: 'medium',
      complexity: 6
    },
    {
      id: 'task-003',
      description: 'Security audit for authentication implementation',
      priority: 'high',
      complexity: 7
    }
  ];
  
  for (const task of demoTasks) {
    console.log(`\n📋 Orchestrating: ${task.description}`);
    const result = await coordinator.orchestrateTask(task, 'adaptive');
    console.log(`  ✅ Completed with ${result.results.length} agents involved`);
    console.log(`  📊 Success: ${result.success}`);
  }
  
  // Show status
  console.log('\n📊 Final Status Report:');
  const status = coordinator.getStatus();
  
  console.log(`\n🧠 Neural Network:`);
  console.log(`  • Accuracy: ${(status.neuralAccuracy * 100).toFixed(1)}%`);
  console.log(`  • Training iterations: ${coordinator.neuralNetwork.trainingHistory.length}`);
  
  console.log(`\n👥 Agent Performance:`);
  const byType = {};
  status.agents.forEach(agent => {
    if (!byType[agent.type]) byType[agent.type] = [];
    byType[agent.type].push(agent.performance);
  });
  
  for (const [type, performances] of Object.entries(byType)) {
    const avg = performances.reduce((a, b) => a + b, 0) / performances.length;
    console.log(`  • ${type}: ${(avg * 100).toFixed(1)}% average performance`);
  }
  
  console.log(`\n📈 Overall Metrics:`);
  console.log(`  • Tasks completed: ${status.performanceMetrics.tasksCompleted}`);
  console.log(`  • Success rate: ${(status.performanceMetrics.successRate * 100).toFixed(1)}%`);
  
  // Save neural state
  console.log('\n💾 Saving neural network state...');
  const neuralState = coordinator.neuralNetwork.save();
  fs.writeFileSync(
    path.join(__dirname, '../ms-neural-state.json'),
    JSON.stringify(neuralState, null, 2)
  );
  console.log('  ✅ Saved to ms-neural-state.json');
  
  console.log('\n🎉 Demo complete!');
}

// Run demo
runDemo().catch(console.error);