# ruv-swarm Implementation Guide

Step-by-step guide for implementing ruv-swarm solutions, from basic setups to advanced shadow workforces.

## 🚀 Quick Start Implementation

### 1. Basic Setup (5 minutes)

```javascript
// Step 1: Add MCP server
claude mcp add ruv-swarm npx ruv-swarm mcp start

// Step 2: Test connection
[BatchTool]:
  mcp__ruv-swarm__daa_init { enableLearning: true, enableCoordination: true }
  mcp__ruv-swarm__swarm_init { topology: "mesh", maxAgents: 3 }
  mcp__ruv-swarm__agent_spawn { type: "researcher", name: "Explorer" }
  mcp__ruv-swarm__agent_spawn { type: "coder", name: "Build<PERSON>" }
  mcp__ruv-swarm__agent_spawn { type: "coordinator", name: "Manager" }
  mcp__ruv-swarm__task_orchestrate { task: "Hello World", strategy: "parallel" }
```

### 2. Configure Hooks

Create `.claude/settings.json`:
```json
{
  "hooks": {
    "pre_edit": "npx ruv-swarm hook pre-task --description \"${file}\"",
    "post_edit": "npx ruv-swarm hook post-edit --file \"${file}\" --train-neural true",
    "pre_commit": "npx ruv-swarm hook notification --message \"Preparing commit\"",
    "post_commit": "npx ruv-swarm hook session-end --export-metrics true"
  }
}
```

## 🏗️ Shadow Workforce Implementation

### Complete 30-Agent Shadow Workforce

```javascript
// MANDATORY: Everything in ONE BatchTool message!
[BatchTool]:
  // 1. Initialize DAA service
  mcp__ruv-swarm__daa_init { 
    enableLearning: true, 
    enableCoordination: true,
    persistenceMode: "auto"
  }
  
  // 2. Create swarm with hierarchical topology
  mcp__ruv-swarm__swarm_init { 
    topology: "hierarchical", 
    maxAgents: 30,
    strategy: "specialized"
  }
  
  // 3. Create directory structure
  Bash("mkdir -p shadow-workforce/{config,daa,workflows,neural,state,test,scripts}")
  
  // 4. Write configuration file
  Write("shadow-workforce/config/shadow-workforce.json", JSON.stringify({
    name: "Mister Smith Shadow Workforce",
    version: "1.0.0",
    topology: "hierarchical",
    teams: {
      core_architecture: {
        lead: "coordinator",
        members: ["coder", "coder", "analyst", "optimizer", "reviewer"],
        focus: "System design and async patterns"
      },
      data_management: {
        lead: "coordinator",
        members: ["coder", "analyst", "optimizer", "tester"],
        focus: "Data storage and message handling"
      },
      security: {
        lead: "coordinator", 
        members: ["analyst", "tester", "reviewer"],
        focus: "Authentication and authorization"
      },
      transport: {
        lead: "coordinator",
        members: ["coder", "optimizer"],
        focus: "gRPC, HTTP, NATS transport"
      },
      operations: {
        lead: "coordinator",
        members: ["coder", "analyst", "documenter"],
        focus: "Deployment and monitoring"
      },
      testing: {
        lead: "tester",
        members: ["tester", "reviewer"],
        focus: "Quality assurance"
      },
      neural_optimization: {
        lead: "optimizer",
        members: ["researcher", "analyst"],
        focus: "Performance and learning"
      },
      coordination: {
        lead: "coordinator",
        members: ["coordinator"],
        focus: "Cross-team coordination"
      }
    }
  }, null, 2))
  
  // 5. Spawn all 30 agents
  // Core Architecture Team (6)
  mcp__ruv-swarm__agent_spawn { type: "coordinator", name: "Core Architecture Lead" }
  mcp__ruv-swarm__agent_spawn { type: "coder", name: "Async Patterns Expert" }
  mcp__ruv-swarm__agent_spawn { type: "coder", name: "Component Architecture Dev" }
  mcp__ruv-swarm__agent_spawn { type: "analyst", name: "System Design Analyst" }
  mcp__ruv-swarm__agent_spawn { type: "optimizer", name: "Performance Optimizer" }
  mcp__ruv-swarm__agent_spawn { type: "reviewer", name: "Architecture Reviewer" }
  
  // Data Management Team (5)
  mcp__ruv-swarm__agent_spawn { type: "coordinator", name: "Data Management Lead" }
  mcp__ruv-swarm__agent_spawn { type: "coder", name: "Database Developer" }
  mcp__ruv-swarm__agent_spawn { type: "analyst", name: "Data Flow Analyst" }
  mcp__ruv-swarm__agent_spawn { type: "optimizer", name: "Query Optimizer" }
  mcp__ruv-swarm__agent_spawn { type: "tester", name: "Data Integrity Tester" }
  
  // Security Team (4)
  mcp__ruv-swarm__agent_spawn { type: "coordinator", name: "Security Lead" }
  mcp__ruv-swarm__agent_spawn { type: "analyst", name: "Security Analyst" }
  mcp__ruv-swarm__agent_spawn { type: "tester", name: "Penetration Tester" }
  mcp__ruv-swarm__agent_spawn { type: "reviewer", name: "Security Reviewer" }
  
  // Transport Team (3)
  mcp__ruv-swarm__agent_spawn { type: "coordinator", name: "Transport Lead" }
  mcp__ruv-swarm__agent_spawn { type: "coder", name: "Protocol Developer" }
  mcp__ruv-swarm__agent_spawn { type: "optimizer", name: "Network Optimizer" }
  
  // Operations Team (4)
  mcp__ruv-swarm__agent_spawn { type: "coordinator", name: "Operations Lead" }
  mcp__ruv-swarm__agent_spawn { type: "coder", name: "DevOps Engineer" }
  mcp__ruv-swarm__agent_spawn { type: "analyst", name: "Monitoring Analyst" }
  mcp__ruv-swarm__agent_spawn { type: "documenter", name: "Operations Documenter" }
  
  // Testing Team (3)
  mcp__ruv-swarm__agent_spawn { type: "tester", name: "QA Lead" }
  mcp__ruv-swarm__agent_spawn { type: "tester", name: "Integration Tester" }
  mcp__ruv-swarm__agent_spawn { type: "reviewer", name: "Test Reviewer" }
  
  // Neural Optimization Team (3)
  mcp__ruv-swarm__agent_spawn { type: "optimizer", name: "Neural Lead" }
  mcp__ruv-swarm__agent_spawn { type: "researcher", name: "ML Researcher" }
  mcp__ruv-swarm__agent_spawn { type: "analyst", name: "Performance Analyst" }
  
  // Coordination Team (2)
  mcp__ruv-swarm__agent_spawn { type: "coordinator", name: "Master Coordinator" }
  mcp__ruv-swarm__agent_spawn { type: "coordinator", name: "Cross-Team Liaison" }
```

### DAA Agent Implementation

```javascript
[BatchTool]:
  Write("shadow-workforce/daa/daa-agent.js", `
class DAAAgent {
  constructor(id, type, config) {
    this.id = id;
    this.type = type;
    this.config = config;
    this.memory = new Map();
    this.learningHistory = [];
    
    // Hook integration
    this.hooks = {
      preTask: 'npx ruv-swarm hook pre-task',
      postEdit: 'npx ruv-swarm hook post-edit',
      notification: 'npx ruv-swarm hook notification',
      postTask: 'npx ruv-swarm hook post-task'
    };
  }
  
  async executeTask(task) {
    // 1. Pre-task coordination
    await this.executeHook('preTask', {
      description: task.description,
      'auto-spawn-agents': false
    });
    
    // 2. Load memory context
    const context = await this.loadContext(task);
    
    // 3. Execute based on type
    const result = await this.executeByType(task, context);
    
    // 4. Store results
    await this.storeResults(task, result);
    
    // 5. Post-task coordination
    await this.executeHook('postTask', {
      'task-id': task.id,
      'analyze-performance': true
    });
    
    return result;
  }
  
  async executeHook(hookType, params) {
    const { spawn } = require('child_process');
    const args = Object.entries(params)
      .map(([k, v]) => \`--\${k}="\${v}"\`)
      .join(' ');
    
    return new Promise((resolve) => {
      const proc = spawn(\`\${this.hooks[hookType]} \${args}\`, { 
        shell: true 
      });
      proc.on('close', resolve);
    });
  }
}

module.exports = DAAAgent;
`)

  Write("shadow-workforce/daa/neural-enhanced-agent.js", `
const DAAAgent = require('./daa-agent');

class NeuralEnhancedAgent extends DAAAgent {
  constructor(id, type, config) {
    super(id, type, config);
    this.cognitivePattern = config.cognitivePattern || 'adaptive';
    this.learningRate = config.learningRate || 0.01;
  }
  
  async learn(experience) {
    // Store experience
    this.learningHistory.push({
      timestamp: Date.now(),
      experience,
      pattern: this.cognitivePattern
    });
    
    // Trigger neural training
    await this.executeHook('notification', {
      message: \`Learning from experience: \${experience.type}\`,
      telemetry: true
    });
    
    // Update internal model
    this.updateCognitiveWeights(experience);
  }
  
  updateCognitiveWeights(experience) {
    // Simplified learning update
    const success = experience.outcome === 'success';
    const adjustment = success ? 
      this.learningRate : 
      -this.learningRate * 0.5;
    
    // Update pattern preference
    this.memory.set('pattern_weight', 
      (this.memory.get('pattern_weight') || 1.0) + adjustment
    );
  }
}

module.exports = NeuralEnhancedAgent;
`)
```

### Workflow Implementation

```javascript
[BatchTool]:
  Write("shadow-workforce/workflows/parallel-development.json", JSON.stringify({
    id: "parallel-dev-workflow",
    name: "Parallel Development Workflow",
    strategy: "parallel",
    steps: [
      {
        id: "analyze-requirements",
        name: "Analyze Requirements",
        agent_type: "analyst",
        parallel_group: 1
      },
      {
        id: "design-architecture",
        name: "Design Architecture", 
        agent_type: "coordinator",
        parallel_group: 1
      },
      {
        id: "implement-core",
        name: "Implement Core Features",
        agent_type: "coder",
        parallel_group: 2
      },
      {
        id: "implement-tests",
        name: "Implement Tests",
        agent_type: "tester",
        parallel_group: 2
      },
      {
        id: "optimize-performance",
        name: "Optimize Performance",
        agent_type: "optimizer",
        parallel_group: 3
      },
      {
        id: "review-code",
        name: "Review Code",
        agent_type: "reviewer",
        parallel_group: 3
      }
    ],
    dependencies: {
      "design-architecture": ["analyze-requirements"],
      "implement-core": ["design-architecture"],
      "implement-tests": ["design-architecture"],
      "optimize-performance": ["implement-core"],
      "review-code": ["implement-core", "implement-tests"]
    }
  }, null, 2))
  
  // Create workflow executor
  Write("shadow-workforce/workflows/workflow-executor.js", `
class WorkflowExecutor {
  constructor(workflow, agents) {
    this.workflow = workflow;
    this.agents = agents;
    this.results = new Map();
  }
  
  async execute() {
    const groups = this.groupByParallel();
    
    for (const [groupId, steps] of groups) {
      // Execute all steps in parallel group
      const promises = steps.map(step => 
        this.executeStep(step)
      );
      
      const results = await Promise.all(promises);
      results.forEach((result, i) => {
        this.results.set(steps[i].id, result);
      });
    }
    
    return this.results;
  }
  
  groupByParallel() {
    const groups = new Map();
    
    this.workflow.steps.forEach(step => {
      const group = step.parallel_group || 0;
      if (!groups.has(group)) {
        groups.set(group, []);
      }
      groups.get(group).push(step);
    });
    
    return Array.from(groups.entries())
      .sort(([a], [b]) => a - b);
  }
  
  async executeStep(step) {
    const agent = this.findAgent(step.agent_type);
    return await agent.executeTask({
      id: step.id,
      description: step.name,
      type: step.agent_type
    });
  }
}

module.exports = WorkflowExecutor;
`)
```

### Setup and Initialization Scripts

```javascript
[BatchTool]:
  Write("shadow-workforce/scripts/setup.sh", `#!/bin/bash
# Shadow Workforce Setup Script

echo "🚀 Setting up Shadow Workforce..."

# Check prerequisites
command -v node >/dev/null 2>&1 || { 
  echo "❌ Node.js is required but not installed."; 
  exit 1; 
}

command -v claude >/dev/null 2>&1 || { 
  echo "❌ Claude CLI is required but not installed."; 
  exit 1; 
}

# Install dependencies
echo "📦 Installing dependencies..."
cd "$(dirname "$0")/.."
npm init -y
npm install child_process fs path

# Verify MCP server
echo "🔌 Checking ruv-swarm MCP server..."
if ! claude mcp list | grep -q "ruv-swarm"; then
  echo "⚙️ Adding ruv-swarm MCP server..."
  claude mcp add ruv-swarm npx ruv-swarm mcp start
fi

# Create state directories
echo "📁 Creating state directories..."
mkdir -p state/{agents,memory,checkpoints}

echo "✅ Shadow Workforce setup complete!"
`)
  
  Bash("chmod +x shadow-workforce/scripts/setup.sh")
  
  Write("shadow-workforce/scripts/start-workforce.js", `#!/usr/bin/env node
const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🐝 Starting Shadow Workforce...');

// Load configuration
const config = JSON.parse(
  fs.readFileSync(path.join(__dirname, '../config/shadow-workforce.json'))
);

// Initialize swarm
async function initializeSwarm() {
  console.log('📡 Initializing swarm...');
  
  // This would normally use MCP calls
  // For demo, we'll simulate the initialization
  console.log('✅ DAA service initialized');
  console.log('✅ Swarm topology: ' + config.topology);
  console.log('✅ Teams configured: ' + Object.keys(config.teams).length);
  console.log('✅ Total agents: 30');
  
  // Start monitoring
  setInterval(() => {
    console.log('💓 Swarm heartbeat - All systems operational');
  }, 30000);
}

initializeSwarm().catch(console.error);
`)
  
  Bash("chmod +x shadow-workforce/scripts/start-workforce.js")
```

## 🧪 Testing Implementation

```javascript
[BatchTool]:
  Write("shadow-workforce/test/smoke-test.js", `
const assert = require('assert');

async function runSmokeTest() {
  console.log('🧪 Running Shadow Workforce Smoke Test...');
  
  try {
    // Test 1: Configuration exists
    const config = require('../config/shadow-workforce.json');
    assert(config.teams, 'Teams configuration missing');
    console.log('✅ Configuration loaded');
    
    // Test 2: DAA Agent can be instantiated
    const DAAAgent = require('../daa/daa-agent');
    const agent = new DAAAgent('test-agent', 'coordinator', {});
    assert(agent.id === 'test-agent', 'Agent ID mismatch');
    console.log('✅ DAA Agent instantiation works');
    
    // Test 3: Workflow structure is valid
    const workflow = require('../workflows/parallel-development.json');
    assert(workflow.steps.length > 0, 'No workflow steps defined');
    console.log('✅ Workflow structure valid');
    
    // Test 4: All agent types are valid
    const validTypes = ['researcher', 'coder', 'analyst', 'optimizer', 
                       'coordinator', 'tester', 'reviewer', 'documenter'];
    const allValid = Object.values(config.teams).every(team =>
      team.members.every(member => validTypes.includes(member))
    );
    assert(allValid, 'Invalid agent types found');
    console.log('✅ All agent types valid');
    
    console.log('\\n🎉 All smoke tests passed!');
    
  } catch (error) {
    console.error('❌ Smoke test failed:', error.message);
    process.exit(1);
  }
}

runSmokeTest();
`)
  
  Write("shadow-workforce/test/integration-test.js", `
async function integrationTest() {
  console.log('🔄 Running Integration Test...');
  
  // Test agent coordination
  const DAAAgent = require('../daa/daa-agent');
  const agent1 = new DAAAgent('agent-1', 'researcher', {});
  const agent2 = new DAAAgent('agent-2', 'coder', {});
  
  // Simulate task execution
  const task = {
    id: 'test-task-1',
    description: 'Research best practices'
  };
  
  // Mock hook execution
  agent1.executeHook = async () => console.log('✓ Hook executed');
  
  await agent1.executeTask(task);
  console.log('✅ Agent task execution complete');
  
  // Test workflow execution
  const WorkflowExecutor = require('../workflows/workflow-executor');
  const workflow = require('../workflows/parallel-development.json');
  
  const executor = new WorkflowExecutor(workflow, [agent1, agent2]);
  console.log('✅ Workflow executor initialized');
  
  console.log('\\n🎉 Integration tests passed!');
}

integrationTest().catch(console.error);
`)
```

## 📊 Monitoring and Metrics

```javascript
[BatchTool]:
  Write("shadow-workforce/scripts/monitor.js", `
#!/usr/bin/env node

const COLORS = {
  green: '\\x1b[32m',
  yellow: '\\x1b[33m',
  red: '\\x1b[31m',
  blue: '\\x1b[34m',
  reset: '\\x1b[0m'
};

function displaySwarmStatus() {
  console.clear();
  console.log(\`
\${COLORS.blue}🐝 Shadow Workforce Monitor\${COLORS.reset}
═══════════════════════════════════════════════

\${COLORS.green}📊 Swarm Status: ACTIVE\${COLORS.reset}
├── 🏗️ Topology: hierarchical
├── 👥 Agents: 30/30 active
├── ⚡ Mode: parallel execution
├── 📊 Tasks: 12 total (4 complete, 6 in-progress, 2 pending)
└── 🧠 Memory: 15 coordination points stored

\${COLORS.yellow}📈 Performance Metrics:\${COLORS.reset}
├── CPU Usage: 45%
├── Memory: 256MB / 512MB
├── Response Time: 120ms avg
└── Task Throughput: 8.5 tasks/min

\${COLORS.green}👥 Team Activity:\${COLORS.reset}
├── 🟢 Core Architecture: Designing module interfaces...
├── 🟢 Data Management: Implementing storage layer...
├── 🟢 Security: Auditing authentication flow...
├── 🟢 Transport: Optimizing gRPC performance...
├── 🟡 Operations: Waiting for deployment scripts...
├── 🟢 Testing: Running integration tests...
├── 🟢 Neural Opt: Training on recent patterns...
└── 🟢 Coordination: Monitoring all teams...

Press Ctrl+C to exit
\`);
}

// Update every 2 seconds
setInterval(displaySwarmStatus, 2000);
displaySwarmStatus();
`)
  
  Bash("chmod +x shadow-workforce/scripts/monitor.js")
```

## 🎯 Common Implementation Patterns

### 1. Error Handling Pattern

```javascript
class RobustAgent extends DAAAgent {
  async executeTask(task) {
    const maxRetries = 3;
    let lastError;
    
    for (let i = 0; i < maxRetries; i++) {
      try {
        return await super.executeTask(task);
      } catch (error) {
        lastError = error;
        await this.handleError(error, i);
      }
    }
    
    throw new Error(`Task failed after ${maxRetries} attempts: ${lastError}`);
  }
  
  async handleError(error, attemptNumber) {
    // Log error
    await this.executeHook('notification', {
      message: `Error on attempt ${attemptNumber + 1}: ${error.message}`,
      telemetry: true
    });
    
    // Exponential backoff
    const delay = Math.pow(2, attemptNumber) * 1000;
    await new Promise(resolve => setTimeout(resolve, delay));
  }
}
```

### 2. Memory Persistence Pattern

```javascript
class PersistentMemoryAgent extends DAAAgent {
  async storeResults(task, result) {
    const key = `task/${task.id}/result`;
    const value = {
      timestamp: Date.now(),
      task: task,
      result: result,
      agent: this.id
    };
    
    // Store in ruv-swarm memory
    await this.executeHook('notification', {
      message: `Storing result for ${task.id}`,
      'memory-key': key,
      'memory-value': JSON.stringify(value)
    });
    
    // Local cache
    this.memory.set(key, value);
  }
  
  async loadContext(task) {
    // Check for related tasks
    const pattern = `task/*/result`;
    const relatedResults = Array.from(this.memory.entries())
      .filter(([k]) => k.match(pattern))
      .map(([_, v]) => v);
    
    return {
      relatedTasks: relatedResults,
      timestamp: Date.now()
    };
  }
}
```

### 3. Adaptive Learning Pattern

```javascript
class AdaptiveAgent extends NeuralEnhancedAgent {
  async executeTask(task) {
    const startTime = Date.now();
    const result = await super.executeTask(task);
    const duration = Date.now() - startTime;
    
    // Learn from performance
    await this.learn({
      type: 'performance',
      task: task.type,
      duration: duration,
      outcome: result.success ? 'success' : 'failure'
    });
    
    // Adapt strategy based on learning
    if (this.memory.get('pattern_weight') < 0.5) {
      this.cognitivePattern = this.selectNewPattern();
    }
    
    return result;
  }
  
  selectNewPattern() {
    const patterns = ['convergent', 'divergent', 'lateral', 
                     'systems', 'critical', 'adaptive'];
    const weights = patterns.map(p => 
      this.memory.get(`pattern/${p}/weight`) || 1.0
    );
    
    // Weighted random selection
    const totalWeight = weights.reduce((a, b) => a + b, 0);
    let random = Math.random() * totalWeight;
    
    for (let i = 0; i < patterns.length; i++) {
      random -= weights[i];
      if (random <= 0) return patterns[i];
    }
    
    return 'adaptive'; // fallback
  }
}
```

## ✅ Implementation Checklist

- [ ] MCP server added and verified
- [ ] Hooks configured in .claude/settings.json
- [ ] Directory structure created
- [ ] Configuration files written
- [ ] All 30 agents spawned successfully
- [ ] DAA implementation complete
- [ ] Workflow definitions created
- [ ] Setup scripts executable
- [ ] Smoke tests passing
- [ ] Integration tests passing
- [ ] Monitoring script functional
- [ ] Error handling implemented
- [ ] Memory persistence working
- [ ] Neural training integrated
- [ ] Documentation complete