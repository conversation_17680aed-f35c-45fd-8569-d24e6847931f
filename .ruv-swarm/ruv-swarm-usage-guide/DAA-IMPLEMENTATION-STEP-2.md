# DAA Implementation Step 2: Workflow Transformation

## Agent-Readable Implementation Trace

### Critical Warning: Common Mistake
```javascript
// ❌ WRONG - From existing IMPLEMENTATION-GUIDE.md
// This creates basic swarm agents, NOT DAA agents!
mcp__ruv-swarm__agent_spawn { type: "coordinator", name: "Core Lead" }

// ✅ CORRECT - For DAA autonomous agents
mcp__ruv-swarm__daa_agent_create { 
  id: "daa-coordinator-001",
  cognitivePattern: "systems",
  enableMemory: true 
}
```

### Step 2.1: Error Identification
```
Original Error: MCP error -32603: DAA tool error: agent[task.method] is not a function
Location: daa-service.js:338
```

### Step 2.2: Root Cause Analysis
```javascript
// INCORRECT format (causes error)
{
  id: "step1",
  action: "analyze",    // ❌ Wrong property
  type: "design"
}

// CORRECT format (works)
{
  id: "step1", 
  task: {               // ✅ Must use 'task' property
    method: "make_decision",  // ✅ Must specify method
    args: [JSON.stringify({   // ✅ Context as JSON string
      context: "analyze architecture"
    })]
  },
  description: "Analyze system architecture"
}
```

### Step 2.3: Workflow Transformation Function
```javascript
transformWorkflowToDAAFormat(workflowConfig) {
  const daaSteps = [];
  
  if (workflowConfig.stages) {
    workflowConfig.stages.forEach(stage => {
      stage.steps.forEach(step => {
        daaSteps.push({
          id: step.id,
          task: {
            method: 'make_decision',
            args: [JSON.stringify({
              context: step.name,
              domain: step.domain,
              agentType: step.agent_type,
              action: 'execute'
            })]
          },
          description: step.name,
          metadata: {
            originalStage: stage.id,
            domain: step.domain,
            agentType: step.agent_type
          }
        });
      });
    });
  }
  
  return {
    id: workflowConfig.id,
    name: workflowConfig.name,
    steps: daaSteps,
    dependencies: workflowConfig.dependencies || {},
    strategy: workflowConfig.strategy || 'adaptive'
  };
}
```

### Step 2.4: Create Compatible Workflow
```javascript
mcp__ruv-swarm__daa_workflow_create({
  id: "test-workflow-fixed-001",
  name: "Fixed MS Framework Workflow",
  steps: [/* transformed steps */],
  dependencies: {},
  strategy: "adaptive"
})
```

### Step 2.5: Fix Existing Workflows
```javascript
// Transform old workflow format from IMPLEMENTATION-GUIDE.md
const oldWorkflow = {
  steps: [
    {
      id: "analyze-requirements",
      name: "Analyze Requirements",
      agent_type: "analyst",  // Old format
      parallel_group: 1
    }
  ]
};

// Apply transformation
const daaWorkflow = transformWorkflowToDAAFormat(oldWorkflow);
```

### Implementation Result
- Original stages: 5
- Transformed DAA steps: 16
- Dependencies preserved: 15
- Execution time: 0-7ms
- Error resolved: No more "agent[task.method] is not a function"

### Key Discovery: daa-service.js Line 338
```javascript
// The actual code that was failing:
if (typeof task === 'function') {
  return await task(agent);
}
// This line requires task.method to exist:
return await agent[task.method](...(task.args || []));
```

---
*For agent consumption only - Step 2 of 3*