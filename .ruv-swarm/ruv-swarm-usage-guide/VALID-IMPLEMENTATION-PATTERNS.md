# Valid Implementation Patterns from Original Guide

These patterns from the original IMPLEMENTATION-GUIDE.md remain valid and useful.

## 🔧 Basic Setup (Still Valid)

### MCP Server Setup
```bash
# Add MCP server
claude mcp add ruv-swarm npx ruv-swarm mcp start

# Verify connection
claude mcp list | grep ruv-swarm
```

### Hooks Configuration
```json
{
  "hooks": {
    "pre_edit": "npx ruv-swarm hook pre-task --description \"${file}\"",
    "post_edit": "npx ruv-swarm hook post-edit --file \"${file}\" --train-neural true",
    "pre_commit": "npx ruv-swarm hook notification --message \"Preparing commit\"",
    "post_commit": "npx ruv-swarm hook session-end --export-metrics true"
  }
}
```

## 📁 Directory Structure (Valid)

```bash
mkdir -p shadow-workforce/{config,daa,workflows,neural,state,test,scripts}
```

## 🛡️ Error Handling Pattern (Valid)

```javascript
class RobustAgent {
  async executeTask(task) {
    const maxRetries = 3;
    let lastError;
    
    for (let i = 0; i < maxRetries; i++) {
      try {
        return await this.performTask(task);
      } catch (error) {
        lastError = error;
        await this.handleError(error, i);
      }
    }
    
    throw new Error(`Task failed after ${maxRetries} attempts: ${lastError}`);
  }
  
  async handleError(error, attemptNumber) {
    // Log error
    console.error(`Attempt ${attemptNumber + 1} failed:`, error.message);
    
    // Exponential backoff
    const delay = Math.pow(2, attemptNumber) * 1000;
    await new Promise(resolve => setTimeout(resolve, delay));
  }
}
```

## 💾 Memory Persistence Pattern (Valid with Updates)

```javascript
class PersistentMemoryAgent {
  async storeResults(task, result) {
    const key = `task/${task.id}/result`;
    const value = {
      timestamp: Date.now(),
      task: task,
      result: result,
      agent: this.id
    };
    
    // For DAA agents, use hooks
    await this.executeHook('notification', {
      message: `Storing result for ${task.id}`,
      'memory-key': key,
      'memory-value': JSON.stringify(value)
    });
    
    // Local cache
    this.memory.set(key, value);
  }
}
```

## 🧠 Adaptive Learning Pattern (Valid Concept)

```javascript
class AdaptiveAgent {
  async learn(experience) {
    // Store experience
    this.learningHistory.push({
      timestamp: Date.now(),
      experience,
      pattern: this.cognitivePattern
    });
    
    // Adjust cognitive pattern based on performance
    if (experience.outcome === 'success') {
      this.reinforcePattern();
    } else {
      this.exploreNewPattern();
    }
  }
  
  reinforcePattern() {
    // Increase confidence in current pattern
    this.patternConfidence = Math.min(1.0, this.patternConfidence + 0.1);
  }
  
  exploreNewPattern() {
    // Consider switching patterns
    const patterns = ['convergent', 'divergent', 'lateral', 'systems', 'critical', 'adaptive'];
    if (this.patternConfidence < 0.5) {
      this.cognitivePattern = patterns[Math.floor(Math.random() * patterns.length)];
      this.patternConfidence = 0.7; // Reset confidence
    }
  }
}
```

## 📊 Monitoring Display (Valid)

```javascript
function displaySwarmStatus() {
  console.log(`
🐝 Shadow Workforce Monitor
═══════════════════════════════════════════════

📊 Swarm Status: ACTIVE
├── 🏗️ Topology: hierarchical
├── 👥 Agents: 30/30 active
├── ⚡ Mode: parallel execution
├── 📊 Tasks: 12 total (4 complete, 6 in-progress, 2 pending)
└── 🧠 Memory: 15 coordination points stored

📈 Performance Metrics:
├── CPU Usage: 45%
├── Memory: 256MB / 512MB
├── Response Time: 120ms avg
└── Task Throughput: 8.5 tasks/min

Press Ctrl+C to exit
`);
}
```

## ✅ Valid Team Structure

The team organization concept is valid, just needs correct agent creation:
- Core Architecture Team (6 agents)
- Data Management Team (5 agents)
- Security Team (4 agents)
- Transport Team (3 agents)
- Operations Team (4 agents)
- Testing Team (3 agents)
- Neural Optimization Team (3 agents)
- Coordination Team (2 agents)

Total: 30 agents

## 🎯 Key Differences to Remember

1. **Agent Creation**: Use `daa_agent_create`, not `agent_spawn`
2. **Workflow Format**: Must include `task.method` structure
3. **Two Systems**: Basic swarm and DAA are separate
4. **Method**: All DAA agents use `make_decision`

---
*These patterns remain valid when adapted for the correct DAA implementation*