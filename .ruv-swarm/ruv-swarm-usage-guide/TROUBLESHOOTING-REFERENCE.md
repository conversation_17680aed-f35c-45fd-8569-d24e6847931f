# ruv-swarm Troubleshooting Reference

Comprehensive troubleshooting guide for all ruv-swarm issues.

## 🔍 Diagnostic Workflow

### 1. Initial Assessment

```bash
# Quick health check
npx ruv-swarm diagnose --verbose

# MCP server status
claude mcp list | grep ruv-swarm

# Test basic functionality
npx ruv-swarm hook pre-task --description "test" --debug true
```

### 2. Issue Categories

```
┌─────────────────┐
│ Issue Detected  │
└────────┬────────┘
         │
    ┌────┴────┐
    │ Type?   │
    └────┬────┘
         │
┌────────┴────────┬──────────┬──────────┬──────────┐
│ Configuration   │Connection│Performance│  Agent   │
└────────┬────────┴────┬─────┴────┬─────┴────┬─────┘
         │             │          │          │
   Check Config   Check Server  Profile   Validate
```

## 📋 Configuration Issues

### Issue: MCP Server Not Found

**Symptoms:**
- `claude mcp list` doesn't show ruv-swarm
- "MCP server not found" errors

**Solutions:**
```bash
# Solution 1: Reinstall
claude mcp remove ruv-swarm
claude mcp add ruv-swarm npx ruv-swarm mcp start

# Solution 2: Check settings
cat ~/.claude/settings.json | jq '.mcp_servers'

# Solution 3: Manual add
echo '{
  "mcp_servers": {
    "ruv-swarm": {
      "command": "npx",
      "args": ["ruv-swarm", "mcp", "start"],
      "type": "stdio"
    }
  }
}' > ~/.claude/mcp-servers.json
```

### Issue: Invalid Configuration Values

**Symptoms:**
- "Invalid topology" errors
- "Unknown strategy" errors

**Valid Values Reference:**
```javascript
// Topologies
const VALID_TOPOLOGIES = ["mesh", "hierarchical", "ring", "star"];

// Strategies  
const VALID_STRATEGIES = ["balanced", "specialized", "adaptive"];

// Agent Types
const VALID_AGENT_TYPES = [
  "researcher", "coder", "analyst", "optimizer",
  "coordinator", "tester", "reviewer", "documenter"
];

// Cognitive Patterns
const VALID_PATTERNS = [
  "convergent", "divergent", "lateral",
  "systems", "critical", "adaptive"
];
```

### Issue: Hook Configuration Errors

**Symptoms:**
- Hooks not triggering
- "Hook not found" errors

**Solution:**
```json
// Correct .claude/settings.json
{
  "hooks": {
    "pre_edit": "npx ruv-swarm hook pre-task --description \"${file}\" --auto-spawn-agents false",
    "post_edit": "npx ruv-swarm hook post-edit --file \"${file}\" --memory-key \"edit/${file}\"",
    "pre_commit": "npx ruv-swarm hook notification --message \"Commit: ${message}\"",
    "post_commit": "npx ruv-swarm hook session-end --export-metrics true"
  }
}
```

## 🔌 Connection Issues

### Issue: WebSocket Connection Failed

**Symptoms:**
- "WebSocket connection failed" errors
- Timeouts on MCP calls

**Solutions:**
```bash
# Solution 1: Use stdio instead of HTTP
claude mcp remove ruv-swarm
claude mcp add ruv-swarm npx ruv-swarm mcp start

# Solution 2: Check port availability (if using HTTP)
lsof -i :3000  # Check if port is in use
kill -9 $(lsof -t -i:3000)  # Kill process using port

# Solution 3: Increase timeout
export RUV_SWARM_TIMEOUT=30000  # 30 seconds
```

### Issue: Agent Communication Timeout

**Symptoms:**
- "Agent communication timeout" errors
- Agents not responding

**Diagnostic Steps:**
```javascript
// Test agent communication
[BatchTool]:
  mcp__ruv-swarm__swarm_status { verbose: true }
  mcp__ruv-swarm__agent_list { filter: "all" }
  mcp__ruv-swarm__agent_metrics { metric: "all" }

// Check specific agent
mcp__ruv-swarm__agent_metrics { 
  agentId: "problematic-agent-id",
  metric: "all"
}
```

### Issue: Network Partition

**Symptoms:**
- Some agents unreachable
- Partial swarm functionality

**Solutions:**
```javascript
// Reinitialize affected agents
[BatchTool]:
  // Get current topology
  mcp__ruv-swarm__swarm_status { verbose: true }
  
  // Restart with simpler topology
  mcp__ruv-swarm__swarm_init { 
    topology: "star",  // More resilient
    maxAgents: 10,     // Reduce load
    strategy: "balanced"
  }
```

## ⚡ Performance Issues

### Issue: Slow Response Times

**Symptoms:**
- Operations taking >5 seconds
- UI lag or timeouts

**Profiling Commands:**
```javascript
// Run performance diagnostics
[BatchTool]:
  mcp__ruv-swarm__benchmark_run { type: "all", iterations: 5 }
  mcp__ruv-swarm__memory_usage { detail: "detailed" }
  mcp__ruv-swarm__agent_metrics { metric: "performance" }
  mcp__ruv-swarm__daa_performance_metrics { category: "all" }
```

**Optimization Steps:**
1. Reduce agent count
2. Simplify topology (star > ring > hierarchical > mesh)
3. Disable neural training temporarily
4. Clear memory cache

```javascript
// Performance optimization
[BatchTool]:
  // Clear old memory
  mcp__ruv-swarm__memory_usage { 
    action: "clear",
    pattern: "old/*"
  }
  
  // Reduce agents
  mcp__ruv-swarm__swarm_init { 
    topology: "star",
    maxAgents: 5,
    strategy: "specialized"
  }
```

### Issue: High Memory Usage

**Symptoms:**
- "Out of memory" errors
- System slowdown

**Memory Management:**
```javascript
// Check memory usage
mcp__ruv-swarm__memory_usage { detail: "by-agent" }

// Clear specific agent memory
mcp__ruv-swarm__memory_usage { 
  action: "clear",
  pattern: "agent/heavy-agent/*"
}

// Implement memory limits
process.env.RUV_SWARM_MEMORY_LIMIT = "512MB";
```

### Issue: CPU Spikes

**Symptoms:**
- High CPU usage (>80%)
- System unresponsive

**Solutions:**
```bash
# Limit concurrent operations
export RUV_SWARM_MAX_CONCURRENT=3

# Reduce neural network complexity
export RUV_SWARM_NEURAL_LAYERS=2
export RUV_SWARM_NEURAL_NODES=64
```

## 🤖 Agent Issues

### Issue: Agent Spawn Failures

**Common Errors and Solutions:**

```javascript
// Error: "Invalid agent type 'architect'"
// ❌ WRONG
mcp__ruv-swarm__agent_spawn { type: "architect" }

// ✅ CORRECT
mcp__ruv-swarm__agent_spawn { 
  type: "coordinator",
  name: "Chief Architect"
}

// Error: "Maximum agents exceeded"
// Check current count first
mcp__ruv-swarm__agent_list { filter: "all" }

// Then increase limit
mcp__ruv-swarm__swarm_init { 
  topology: "hierarchical",
  maxAgents: 50  // Increase from default
}
```

### Issue: Agent Not Learning

**Symptoms:**
- No improvement over time
- Repeated mistakes

**Diagnostic:**
```javascript
// Check learning status
[BatchTool]:
  mcp__ruv-swarm__daa_learning_status { 
    agentId: "agent-id",
    detailed: true 
  }
  
  mcp__ruv-swarm__neural_status { 
    agentId: "agent-id" 
  }

// Manual training
mcp__ruv-swarm__neural_train { 
  agentId: "agent-id",
  iterations: 50
}
```

### Issue: Agent Coordination Failures

**Symptoms:**
- Agents working on same task
- Conflicting outputs

**Solutions:**
```javascript
// Check cognitive patterns
mcp__ruv-swarm__daa_cognitive_pattern { 
  agent_id: "agent-id",
  action: "analyze"
}

// Reassign patterns for better coordination
[BatchTool]:
  mcp__ruv-swarm__daa_cognitive_pattern { 
    agent_id: "agent-1",
    action: "change",
    pattern: "convergent"
  }
  
  mcp__ruv-swarm__daa_cognitive_pattern { 
    agent_id: "agent-2",
    action: "change",
    pattern: "divergent"
  }
```

## 🛠️ Advanced Debugging

### Enable Debug Mode

```bash
# Set debug environment variables
export RUV_SWARM_DEBUG=true
export RUV_SWARM_LOG_LEVEL=debug
export RUV_SWARM_TRACE=true

# Enable verbose MCP logging
export CLAUDE_MCP_DEBUG=true
```

### Trace Execution Flow

```javascript
// Enable tracing for specific operations
[BatchTool]:
  // Start trace
  Bash("npx ruv-swarm trace start --output trace.log")
  
  // Run problematic operation
  mcp__ruv-swarm__task_orchestrate { 
    task: "Debug this task",
    strategy: "sequential"  // Easier to trace
  }
  
  // Stop trace
  Bash("npx ruv-swarm trace stop")
  
  // Analyze trace
  Bash("npx ruv-swarm trace analyze trace.log")
```

### Memory Dump Analysis

```javascript
// Dump all memory for analysis
mcp__ruv-swarm__memory_usage { 
  action: "list",
  pattern: "*"
} 

// Export to file for analysis
Bash("npx ruv-swarm memory export --output memory-dump.json")
```

## 🔄 Recovery Procedures

### Soft Reset (Preserve Data)

```javascript
[BatchTool]:
  // Stop current operations
  mcp__ruv-swarm__swarm_monitor { duration: 0 }
  
  // Clear active tasks
  mcp__ruv-swarm__memory_usage { 
    action: "clear",
    pattern: "task/active/*"
  }
  
  // Reinitialize
  mcp__ruv-swarm__swarm_init { 
    topology: "mesh",
    maxAgents: 10
  }
```

### Hard Reset (Clean Slate)

```bash
#!/bin/bash
# Complete reset script

# 1. Stop MCP server
claude mcp stop ruv-swarm

# 2. Clear all caches
rm -rf ~/.ruv-swarm/cache
rm -rf ~/.ruv-swarm/state
rm -rf ~/.ruv-swarm/memory

# 3. Remove configuration
claude mcp remove ruv-swarm

# 4. Reinstall fresh
claude mcp add ruv-swarm npx ruv-swarm mcp start

echo "✅ Hard reset complete"
```

### Emergency Recovery

```javascript
// If nothing else works
[BatchTool]:
  // Kill all processes
  Bash("pkill -f ruv-swarm")
  
  // Clear IPC sockets
  Bash("rm -f /tmp/ruv-swarm-*.sock")
  
  // Reset Claude MCP
  Bash("claude mcp restart")
  
  // Wait for cleanup
  Bash("sleep 5")
  
  // Start fresh
  mcp__ruv-swarm__daa_init { 
    enableLearning: false,  // Start simple
    enableCoordination: false 
  }
```

## 📊 Monitoring Commands

### Real-time Monitoring

```bash
# Watch swarm status
watch -n 2 'npx ruv-swarm status'

# Monitor specific agent
npx ruv-swarm agent watch <agent-id>

# Track memory usage
npx ruv-swarm memory monitor --interval 1000
```

### Performance Metrics

```javascript
// Comprehensive metrics collection
[BatchTool]:
  mcp__ruv-swarm__benchmark_run { type: "all" }
  mcp__ruv-swarm__daa_performance_metrics { 
    category: "all",
    timeRange: "1h"
  }
  mcp__ruv-swarm__memory_usage { detail: "detailed" }
  mcp__ruv-swarm__neural_status { }
```

## 🚨 Error Code Reference

| Code | Error | Solution |
|------|-------|----------|
| E001 | Invalid agent type | Use valid type from list |
| E002 | MCP server not found | Reinstall ruv-swarm |
| E003 | Topology not supported | Use: mesh, star, ring, hierarchical |
| E004 | Memory limit exceeded | Clear cache, reduce agents |
| E005 | Agent communication timeout | Check network, reduce load |
| E006 | DAA not initialized | Run daa_init first |
| E007 | Workflow not found | Check workflow ID |
| E008 | Neural training failed | Check data, reduce batch size |
| E009 | Hook execution failed | Verify hook configuration |
| E010 | Session restore failed | Clear state, start fresh |

## 💡 Pro Tips

1. **Always check initialization order**: DAA → Swarm → Agents
2. **Use BatchTool**: Never send sequential messages
3. **Monitor regularly**: Set up monitoring before issues arise
4. **Keep logs**: Enable debug logging for production
5. **Test hooks**: Verify hooks work before relying on them
6. **Document issues**: Keep notes on what fixed problems