/**
 * State Manager for DAA persistence
 */

const fs = require('fs').promises;
const path = require('path');

class StateManager {
  constructor() {
    this.statePath = path.join(__dirname, '..', 'state');
    this.memory = new Map();
  }
  
  async initialize() {
    await fs.mkdir(this.statePath, { recursive: true });
  }
  
  async saveState(key, value) {
    this.memory.set(key, value);
    const filePath = path.join(this.statePath, `${key}.json`);
    await fs.writeFile(filePath, JSON.stringify(value, null, 2));
  }
  
  async loadState(key) {
    if (this.memory.has(key)) return this.memory.get(key);
    
    try {
      const filePath = path.join(this.statePath, `${key}.json`);
      const data = await fs.readFile(filePath, 'utf8');
      const value = JSON.parse(data);
      this.memory.set(key, value);
      return value;
    } catch {
      return null;
    }
  }
  
  async listStates() {
    const files = await fs.readdir(this.statePath);
    return files.filter(f => f.endsWith('.json')).map(f => f.replace('.json', ''));
  }
}

module.exports = StateManager;