const assert = require('assert');
const fs = require('fs');
const path = require('path');

async function runSmokeTest() {
  console.log('🧪 Running Mister Smith Shadow Workforce Smoke Test...\n');
  
  try {
    // Test 1: Configuration exists
    const configPath = path.join(__dirname, '../config/shadow-workforce.json');
    assert(fs.existsSync(configPath), 'Configuration file missing');
    const config = require(configPath);
    assert(config.teams, 'Teams configuration missing');
    assert(config.totalAgents === 50, 'Agent count mismatch');
    console.log('✅ Configuration loaded (50 agents)');
    
    // Test 2: DAA Agent can be loaded
    const daaPath = path.join(__dirname, '../../daa-neural-network/src/daa-agent.js');
    assert(fs.existsSync(daaPath), 'DAA Agent module missing');
    const DAAAgent = require(daaPath);
    const agent = new DAAAgent('test-agent', 'coordinator', {});
    assert(agent.id === 'test-agent', 'Agent ID mismatch');
    console.log('✅ DAA Agent module functional');
    
    // Test 3: Neural Network can be loaded
    const neuralPath = path.join(__dirname, '../../daa-neural-network/src/neural-network.js');
    assert(fs.existsSync(neuralPath), 'Neural Network module missing');
    const NeuralNetwork = require(neuralPath);
    const network = new NeuralNetwork();
    assert(network.inputSize === 10, 'Neural network config mismatch');
    console.log('✅ Neural Network module functional');
    
    // Test 4: Workflow structure is valid
    const workflowPath = path.join(__dirname, '../workflows/ms-development-workflow.json');
    assert(fs.existsSync(workflowPath), 'Workflow file missing');
    const workflow = require(workflowPath);
    assert(workflow.stages.length > 0, 'No workflow stages defined');
    assert(workflow.strategy === 'adaptive', 'Workflow strategy mismatch');
    console.log('✅ Workflow structure valid');
    
    // Test 5: All agent types are valid
    const validTypes = ['researcher', 'coder', 'analyst', 'optimizer', 
                       'coordinator', 'tester', 'reviewer', 'documenter'];
    let allValid = true;
    Object.values(config.teams).forEach(team => {
      if (team.lead && !validTypes.includes(team.lead)) allValid = false;
      team.members.forEach(member => {
        if (!validTypes.includes(member)) allValid = false;
      });
    });
    assert(allValid, 'Invalid agent types found');
    console.log('✅ All agent types valid');
    
    // Test 6: Cognitive distribution adds up
    const totalCognitive = Object.values(config.cognitiveDistribution)
      .reduce((sum, count) => sum + count, 0);
    assert(totalCognitive === 50, 'Cognitive distribution mismatch');
    console.log('✅ Cognitive distribution correct');
    
    // Test 7: All domains covered
    assert(config.specializedDomains.length === 15, 'Domain count mismatch');
    console.log('✅ All 15 MS Framework domains covered');
    
    // Test 8: Scripts are executable
    const scriptsDir = path.join(__dirname, '../scripts');
    const scripts = ['setup.sh', 'start-workforce.js', 'monitor.js'];
    scripts.forEach(script => {
      const scriptPath = path.join(scriptsDir, script);
      assert(fs.existsSync(scriptPath), `Script ${script} missing`);
    });
    console.log('✅ All scripts present');
    
    console.log('\n🎉 All smoke tests passed!');
    console.log('\n📊 Summary:');
    console.log('├── Total Agents: 50');
    console.log('├── Teams: ' + Object.keys(config.teams).length);
    console.log('├── Domains: 15');
    console.log('├── Workflows: Configured');
    console.log('└── Neural Network: Ready');
    
  } catch (error) {
    console.error('❌ Smoke test failed:', error.message);
    process.exit(1);
  }
}

runSmokeTest();