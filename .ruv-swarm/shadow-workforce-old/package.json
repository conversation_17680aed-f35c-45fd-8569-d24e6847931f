{"name": "mister-smith-shadow-workforce", "version": "1.0.0", "description": "Shadow workforce implementation for Mister Smith system with neural optimization", "main": "src/shadow-workforce.js", "scripts": {"start": "node scripts/start-workforce.js", "demo": "node ../daa-neural-network/scripts/demo.js", "orchestrate": "node src/shadow-workforce-orchestrator.js", "test": "node test/smoke-test.js", "train": "node neural/train.js", "monitor": "node scripts/monitor.js", "install-ruv": "npm install -g ruv-swarm", "bridge": "node scripts/daa-bridge.js", "solution": "node scripts/daa-integration-solution.js"}, "dependencies": {"ruv-swarm": "latest"}, "devDependencies": {"jest": "^29.0.0"}, "keywords": ["shadow-workforce", "neural-network", "mister-smith", "daa", "swarm"], "author": "Mister Smith Development Team", "license": "MIT"}