#!/usr/bin/env bash
# Claude Code Direct Swarm Invocation Helper
# Generated by ruv-swarm --claude setup

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${GREEN}🐝 ruv-swarm Claude Code Direct Invocation${NC}"
echo "============================================="
echo

# Function to invoke <PERSON> with swarm commands
invoke_claude_swarm() {
    local prompt="$1"
    local skip_permissions="$2"
    
    echo -e "${YELLOW}🚀 Invoking Claude Code with swarm integration...${NC}"
    echo "Prompt: $prompt"
    echo
    
    if [ "$skip_permissions" = "true" ]; then
        echo -e "${RED}⚠️  Using --dangerously-skip-permissions flag${NC}"
        claude "$prompt" --dangerously-skip-permissions
    else
        claude "$prompt"
    fi
}

# Predefined swarm prompts with remote support
case "$1" in
    "research")
        invoke_claude_swarm "Initialize a research swarm with 5 agents using ruv-swarm. Create researcher, analyst, and coder agents. Then orchestrate the task: $2" "$3"
        ;;
    "development")
        invoke_claude_swarm "Initialize a development swarm with 8 agents using ruv-swarm in hierarchical topology. Create architect, frontend coder, backend coder, and tester agents. Then orchestrate the task: $2" "$3"
        ;;
    "analysis")
        invoke_claude_swarm "Initialize an analysis swarm with 6 agents using ruv-swarm. Create multiple analyst agents with different specializations. Then orchestrate the task: $2" "$3"
        ;;
    "optimization")
        invoke_claude_swarm "Initialize an optimization swarm with 4 agents using ruv-swarm. Create optimizer and analyst agents. Then orchestrate the performance optimization task: $2" "$3"
        ;;
    "custom")
        invoke_claude_swarm "$2" "$3"
        ;;
    "help")
        echo -e "${GREEN}Usage:${NC}"
        echo "  ./claude-swarm.sh research \"task description\" [skip-permissions]"
        echo "  ./claude-swarm.sh development \"task description\" [skip-permissions]"
        echo "  ./claude-swarm.sh analysis \"task description\" [skip-permissions]"
        echo "  ./claude-swarm.sh optimization \"task description\" [skip-permissions]"
        echo "  ./claude-swarm.sh custom \"full claude prompt\" [skip-permissions]"
        echo
        echo -e "${GREEN}Examples:${NC}"
        echo '  ./claude-swarm.sh research "Analyze modern web frameworks" true'
        echo '  ./claude-swarm.sh development "Build user authentication API"'
        echo '  ./claude-swarm.sh custom "Initialize ruv-swarm and create 3 agents for data processing"'
        echo
        echo -e "${YELLOW}Note:${NC} Add 'true' as the last parameter to use --dangerously-skip-permissions"
        ;;
    *)
        echo -e "${RED}Unknown command: $1${NC}"
        echo "Run './claude-swarm.sh help' for usage information"
        exit 1
        ;;
esac