{"metadata": {"timestamp": "2025-07-04T06:03:15.409Z", "version": "0.2.0", "testType": "comprehensive", "iterations": 5, "system": {"platform": "darwin", "arch": "arm64", "nodeVersion": "v24.3.0"}}, "benchmarks": {"wasmLoading": {"time": 51, "target": 100, "status": "PASS"}, "swarmInit": {"times": [6, 6, 5, 6, 6], "average": 5.8, "min": 5, "max": 6, "target": 10, "status": "PASS"}, "agentSpawn": {"times": [3, 3, 4, 3, 4], "average": 3.4, "target": 5, "status": "PASS"}, "neuralProcessing": {"times": [21, 21, 21, 21, 21], "average": 21, "throughput": 47.61904761904762, "target": 50, "status": "PASS"}, "memory": {"heapUsed": 8901056, "heapTotal": 17842176, "external": 21755610, "rss": 63242240, "efficiency": "49.9"}}, "overallScore": 80}