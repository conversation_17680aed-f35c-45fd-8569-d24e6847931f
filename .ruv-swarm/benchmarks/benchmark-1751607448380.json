{"metadata": {"timestamp": "2025-07-04T05:37:28.134Z", "version": "0.2.0", "testType": "comprehensive", "iterations": 10, "system": {"platform": "darwin", "arch": "arm64", "nodeVersion": "v24.3.0"}}, "benchmarks": {"wasmLoading": {"time": 51, "target": 100, "status": "PASS"}, "swarmInit": {"times": [6, 5, 6, 6, 6, 4, 6, 5, 6, 5], "average": 5.5, "min": 4, "max": 6, "target": 10, "status": "PASS"}, "agentSpawn": {"times": [3, 3, 4, 3, 4, 3, 3, 4, 3, 4], "average": 3.4, "target": 5, "status": "PASS"}, "neuralProcessing": {"times": [21, 21, 21, 20, 20], "average": 20.6, "throughput": 48.543689320388346, "target": 50, "status": "PASS"}, "memory": {"heapUsed": 8927624, "heapTotal": 18104320, "external": 21755610, "rss": 62488576, "efficiency": "49.3"}}, "overallScore": 80}